[package]
name = "workov"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio-postgres = { version = "0.7.13", features = ["with-chrono-0_4"] }
tokio = { version = "1", features = ["full"] }
tokio-cron-scheduler = "0.14.0"
reqwest = "0.12.15"
axum = "0.8.4"
quick-xml = { version = "0.37.5", features = ["serialize"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
dotenvy = "0.15.7"
anyhow = "1.0.98"
chrono = { version = "0.4.41", features = ["serde"] }
chinese_holiday = "2025.1.1"
uuid = { version = "1.16.0", features = ["v4","fast-rng"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["fmt", "env-filter", "json", "time", "local-time"] }
tracing-appender = "0.2"
mimalloc = "0.1.46"
openssl = { version = "0.10.72", features = ["vendored"] }
chrono-tz = "0.10.3"

[build-dependencies]
dotenvy = "0.15.7"
